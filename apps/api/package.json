{"name": "api", "version": "0.1.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "cross-env NODE_ENV=prod node dist/src/main", "start:prod:pm": "cross-env NODE_ENV=prod pm2 start ecosystem.config.js", "test": "pnpm test:unit && pnpm test:e2e", "test:unit": "cross-env NODE_ENV=test jest --config jest.config.ts", "test:e2e": "cross-env NODE_ENV=test jest --config jest.config.e2e.ts", "test:cov": "jest --coverage"}, "dependencies": {"drizzle-orm": "^0.32.0", "@earthworm/schema": "workspace:^", "@nestjs-modules/ioredis": "^2.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.1", "@nestjs/swagger": "^7.1.17", "argon2": "^0.31.2", "axios": "^1.6.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "ioredis": "^5.3.2", "jose": "^5.2.4", "postgres": "^3.4.4", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "start": "^5.1.0", "superagent": "^8.1.2"}, "devDependencies": {"@electric-sql/pglite": "^0.1.2", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@paralleldrive/cuid2": "2.2.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "cross-env": "^7.0.3", "ioredis-mock": "^8.9.0", "jest": "^29.5.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}}