import { Injectable } from "@nestjs/common";
import * as superagent from "superagent";

@Injectable()
export class ToolService {
  async dailySentence() {
    // 这里需要替换为日语每日一句的API
    // 可以考虑使用 https://jlpt-sensei.com/api/ 或其他日语API
    try {
      const { text } = await superagent.get("https://open.iciba.com/dsapi/");
      const data = JSON.parse(text);
      // 这里假设API返回的是中日双语内容
      // 实际使用时需要根据真实API调整
      const res = {
        zh: data.note,
        ja: data.content, // 将英语替换为日语
      };
      return res;
    } catch (error) {
      // 如果API调用失败，返回默认内容
      return {
        zh: "学习是一生的事业。",
        ja: "学ぶことは一生の仕事です。",
      };
    }
  }
}
