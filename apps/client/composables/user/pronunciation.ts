import { ref } from "vue";

export function usePronunciation() {
  const pronunciationType = ref("ja"); // 默认为日语发音

  function getPronunciationUrl(text?: string) {
    if (!text) return "";

    // 使用 Google TTS API 获取日语发音
    // 注意：实际使用时可能需要替换为其他支持日语的TTS服务
    return `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(
      text,
    )}&tl=ja&client=tw-ob`;
  }

  return {
    pronunciationType,
    getPronunciationUrl,
  };
}
