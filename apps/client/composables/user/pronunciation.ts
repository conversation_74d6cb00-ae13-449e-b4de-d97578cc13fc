import { ref } from "vue";

export function usePronunciation() {
  const pronunciationType = ref("ja"); // 默认为日语发音

  function getPronunciationUrl(text?: string) {
    if (!text) return "";

    // 暂时禁用音频播放，返回空字符串避免错误
    // TODO: 集成可靠的日语TTS服务
    console.log("音频播放功能暂时禁用，文本:", text);
    return "";

    // 备用：如果需要启用音频，可以尝试以下URL
    // return `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(text)}&tl=ja&client=gtx`;
  }

  return {
    pronunciationType,
    getPronunciationUrl,
  };
}
