import { ref } from "vue";

export function usePronunciation() {
  const pronunciationType = ref("ja"); // 默认为日语发音

  function getPronunciationUrl(text?: string) {
    if (!text) return "";

    // 尝试多个TTS服务，提高成功率
    const services = [
      // Google TTS (主要)
      `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(text)}&tl=ja&client=gtx`,
      // 备用服务
      `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(text)}&tl=ja&client=tw-ob`,
    ];

    // 返回第一个服务URL，如果失败会在audio.ts中处理
    return services[0];
  }

  return {
    pronunciationType,
    getPronunciationUrl,
  };
}
