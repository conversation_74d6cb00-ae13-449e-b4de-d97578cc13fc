import { ref } from "vue";

export function usePronunciation() {
  const pronunciationType = ref("ja"); // 默认为日语发音

  function getPronunciationUrl(text?: string) {
    if (!text) return "";

    // 使用可靠的Google TTS API
    try {
      const url = `https://translate.google.com/translate_tts?ie=UTF-8&q=${encodeURIComponent(text)}&tl=ja&client=gtx&ttsspeed=0.5`;
      console.log("生成音频URL:", url);
      return url;
    } catch (error) {
      console.warn("生成音频URL失败:", error);
      return "";
    }
  }

  return {
    pronunciationType,
    getPronunciationUrl,
  };
}
