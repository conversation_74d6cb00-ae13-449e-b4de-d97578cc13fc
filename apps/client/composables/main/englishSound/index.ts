import { useCourseStore } from "~/store/course";

// 重命名为 japaneseSound
export function useCurrentStatementJapaneseSound() {
  const courseStore = useCourseStore();

  return {
    playSound: () => {
      // 直接使用 Web Speech API 播放日语
      const word = courseStore.currentStatement?.japanese;
      if (word && "speechSynthesis" in window) {
        try {
          const utterance = new SpeechSynthesisUtterance(word);
          utterance.lang = "ja-JP";
          utterance.rate = 0.8;
          utterance.pitch = 1;
          speechSynthesis.speak(utterance);
          console.log("使用 Web Speech API 播放日语:", word);
        } catch (error) {
          console.warn("语音合成失败:", error);
        }
      }

      // 返回一个空的停止函数
      return () => {};
    },
  };
}

// 朗读每日一句
export function readOneSentencePerDayAloud(str: string) {
  if (str && "speechSynthesis" in window) {
    try {
      const utterance = new SpeechSynthesisUtterance(str);
      utterance.lang = "ja-JP";
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    } catch (error) {
      console.warn("每日一句语音合成失败:", error);
    }
  }
}

export function playJapanese(japanese: string) {
  if (japanese && "speechSynthesis" in window) {
    try {
      const utterance = new SpeechSynthesisUtterance(japanese);
      utterance.lang = "ja-JP";
      utterance.rate = 0.8;
      utterance.pitch = 1;
      speechSynthesis.speak(utterance);
    } catch (error) {
      console.warn("日语语音合成失败:", error);
    }
  }
}
