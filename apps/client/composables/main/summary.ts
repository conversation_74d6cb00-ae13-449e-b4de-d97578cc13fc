import { onMounted, ref } from "vue";

import { fetchDailySentence } from "~/api/tool";

const showModal = ref(false);
export function useSummary() {
  function showSummary() {
    showModal.value = true;
  }

  function hideSummary() {
    showModal.value = false;
  }

  return {
    showModal,
    showSummary,
    hideSummary,
  };
}

export const defaultJaSentence = "生きるべきか、死ぬべきか、それが問題だ。";
export const defaultZhSentence = "生存还是毁灭，这是一个问题。";

const jaSentence = ref(defaultJaSentence);
const zhSentence = ref(defaultZhSentence);
const hasLoadingDailySentence = ref(false);

export const resetSentenceLoading = () => (hasLoadingDailySentence.value = false);

export function useDailySentence() {
  const getDailySentence = async () => {
    if (!hasLoadingDailySentence.value) {
      hasLoadingDailySentence.value = true;
      const { ja, zh } = await fetchDailySentence().catch((err) => {
        hasLoadingDailySentence.value = false;
        return Promise.reject(err);
      });
      jaSentence.value = ja;
      zhSentence.value = zh;
    }
  };

  onMounted(() => {
    getDailySentence();
  });

  return {
    jaSentence,
    zhSentence,
    getDailySentence,
  };
}
