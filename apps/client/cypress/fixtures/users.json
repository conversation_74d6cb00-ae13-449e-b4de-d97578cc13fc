[{"id": 1, "name": "<PERSON><PERSON>", "username": "<PERSON><PERSON>", "email": "<EMAIL>", "address": {"street": "<PERSON><PERSON>", "suite": "Apt. 556", "city": "Gwenborough", "zipcode": "92998-3874", "geo": {"lat": "-37.3159", "lng": "81.1496"}}, "phone": "************** x56442", "website": "hildegard.org", "company": {"name": "Romaguera-Crona", "catchPhrase": "Multi-layered client-server neural-net", "bs": "harness real-time e-markets"}}, {"id": 2, "name": "<PERSON><PERSON>", "username": "<PERSON><PERSON>", "email": "<PERSON><EMAIL>", "address": {"street": "Victor <PERSON>", "suite": "Suite 879", "city": "Wisokyburgh", "zipcode": "90566-7771", "geo": {"lat": "-43.9509", "lng": "-34.4618"}}, "phone": "************ x09125", "website": "anastasia.net", "company": {"name": "<PERSON><PERSON>-<PERSON><PERSON>", "catchPhrase": "Proactive didactic contingency", "bs": "synergize scalable supply-chains"}}, {"id": 3, "name": "<PERSON><PERSON>", "username": "<PERSON>", "email": "<PERSON>@yesenia.net", "address": {"street": "Douglas Extension", "suite": "Suite 847", "city": "McKenziehaven", "zipcode": "59590-4157", "geo": {"lat": "-68.6102", "lng": "-47.0653"}}, "phone": "**************", "website": "ramiro.info", "company": {"name": "Romaguera-<PERSON><PERSON>", "catchPhrase": "Face to face bifurcated interface", "bs": "e-enable strategic applications"}}, {"id": 4, "name": "<PERSON>", "username": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "address": {"street": "Hoeger Mall", "suite": "Apt. 692", "city": "South Elvis", "zipcode": "53919-4257", "geo": {"lat": "29.4572", "lng": "-164.2990"}}, "phone": "************ x156", "website": "kale.biz", "company": {"name": "Robel-Corkery", "catchPhrase": "Multi-tiered zero tolerance productivity", "bs": "transition cutting-edge web services"}}, {"id": 5, "name": "<PERSON><PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON><PERSON>@annie.ca", "address": {"street": "<PERSON>les Walks", "suite": "Suite 351", "city": "Roscoeview", "zipcode": "33263", "geo": {"lat": "-31.8129", "lng": "62.5342"}}, "phone": "(254)954-1289", "website": "demarco.info", "company": {"name": "Keebler LLC", "catchPhrase": "User-centric fault-tolerant solution", "bs": "revolutionize end-to-end systems"}}, {"id": 6, "name": "Mrs. <PERSON>", "username": "<PERSON><PERSON><PERSON>", "email": "<PERSON><PERSON>_<PERSON>@jasper.info", "address": {"street": "Norberto Crossing", "suite": "Apt. 950", "city": "South Christy", "zipcode": "23505-1337", "geo": {"lat": "-71.4197", "lng": "71.7478"}}, "phone": "************** x6430", "website": "ola.org", "company": {"name": "Considine-Lockman", "catchPhrase": "Synchronised bottom-line interface", "bs": "e-enable innovative applications"}}, {"id": 7, "name": "<PERSON><PERSON>", "username": "Elwyn<PERSON>", "email": "Telly.Ho<PERSON><EMAIL>", "address": {"street": "Rex Trail", "suite": "Suite 280", "city": "Howemouth", "zipcode": "58804-1099", "geo": {"lat": "24.8918", "lng": "21.8984"}}, "phone": "************", "website": "elvis.io", "company": {"name": "Johns Group", "catchPhrase": "Configurable multimedia task-force", "bs": "generate enterprise e-tailers"}}, {"id": 8, "name": "<PERSON>", "username": "<PERSON><PERSON>_Nienow", "email": "<PERSON>@rosamond.me", "address": {"street": "Ellsworth Summit", "suite": "Suite 729", "city": "Aliyaview", "zipcode": "45169", "geo": {"lat": "-14.3990", "lng": "-120.7677"}}, "phone": "************ x140", "website": "jacynthe.com", "company": {"name": "Abernathy Group", "catchPhrase": "Implemented secondary concept", "bs": "e-enable extensible e-tailers"}}, {"id": 9, "name": "<PERSON><PERSON>", "username": "Delphine", "email": "<PERSON><PERSON>_<PERSON>@dana.io", "address": {"street": "Dayna Park", "suite": "Suite 449", "city": "Bartholomebury", "zipcode": "76495-3109", "geo": {"lat": "24.6463", "lng": "-168.8889"}}, "phone": "(775)976-6794 x41206", "website": "conrad.com", "company": {"name": "Yost and Sons", "catchPhrase": "Switchable contextually-based project", "bs": "aggregate real-time technologies"}}, {"id": 10, "name": "<PERSON><PERSON>", "username": "<PERSON><PERSON><PERSON><PERSON>", "email": "<PERSON>.<PERSON><EMAIL>", "address": {"street": "<PERSON><PERSON>", "suite": "Suite 198", "city": "Lebsackbury", "zipcode": "31428-2261", "geo": {"lat": "-38.2386", "lng": "57.2232"}}, "phone": "************", "website": "ambrose.net", "company": {"name": "Hoeger LLC", "catchPhrase": "Centralized empowering task-force", "bs": "target end-to-end models"}}]