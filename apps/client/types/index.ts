export * from "./models/user";
export * from "./models/membership";
export * from "./models/course";
export * from "./models/course-history";
export * from "./models/course-pack";
export * from "./models/tool";
export * from "./models/mastered-elements";
export * from "./models/rank";
export * from "./models/user-course-progress";

export interface Statement {
  id: string;
  order: number;
  chinese: string;
  english?: string; // 保留英语字段以兼容
  japanese?: string; // 新增日语字段，暂时可选
  hiragana?: string;
  romaji?: string; // 罗马音，暂时可选
  soundmark: string; // 保留音标字段
  isMastered: boolean;
}

export interface DailySentence {
  zh: string;
  ja: string;
}
