export * from "./models/user";
export * from "./models/membership";
export * from "./models/course";
export * from "./models/course-history";
export * from "./models/course-pack";
export * from "./models/tool";
export * from "./models/mastered-elements";
export * from "./models/rank";
export * from "./models/user-course-progress";

export interface Statement {
  id: string;
  order: number;
  chinese: string;
  japanese: string;
  hiragana?: string;
  romaji: string;
  isMastered: boolean;
}

export interface DailySentence {
  zh: string;
  ja: string;
}
