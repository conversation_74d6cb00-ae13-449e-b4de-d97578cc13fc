<template>
  <div class="text-center">
    <div class="mb-4 mt-10 text-2xl dark:text-gray-50">
      {{ courseStore.currentStatement?.chinese || "生存还是毁灭，这是一个问题" }}
    </div>
    <MainQuestionInput />
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from "vue";

import { useCurrentStatementJapaneseSound } from "~/composables/main/englishSound";
import { useAutoPlayJapanese } from "~/composables/user/sound";
import { useCourseStore } from "~/store/course";

const courseStore = useCourseStore();
const { playSound } = useCurrentStatementJapaneseSound();
const { isAutoPlayJapanese } = useAutoPlayJapanese();

onMounted(() => {
  handleAutoPlayJapanese();
});

watch(
  () => courseStore.currentStatement,
  () => {
    handleAutoPlayJapanese();
  },
);

function handleAutoPlayJapanese() {
  if (isAutoPlayJapanese()) {
    playSound();
  }
}
</script>
