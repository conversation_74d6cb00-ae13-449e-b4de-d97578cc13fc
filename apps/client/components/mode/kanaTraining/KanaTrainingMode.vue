<template>
  <div class="flex h-full items-center justify-center">
    <template v-if="isQuestion()">
      <div class="text-center">
        <div class="mb-4 mt-10 text-2xl dark:text-gray-50">
          {{ currentKana.character }}
        </div>
        <input
          v-model="userInput"
          class="input input-bordered w-full max-w-xs"
          placeholder="请输入罗马音"
          @keyup.enter="checkAnswer"
        />
        <div class="mt-4">
          <button
            class="btn btn-primary"
            @click="checkAnswer"
          >
            确认
          </button>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="text-center">
        <div class="text-2xl">
          <div>{{ currentKana.character }}</div>
          <div class="mt-2 text-green-500">{{ currentKana.romaji }}</div>
        </div>
        <div class="mt-6">
          <button
            class="btn btn-outline btn-sm"
            @