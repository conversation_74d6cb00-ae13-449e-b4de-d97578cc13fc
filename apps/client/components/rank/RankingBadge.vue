<template>
  <div class="flex justify-center text-center text-base text-orange-500">
    <template v-if="[1, 2, 3].includes(rank)">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="32"
        height="32"
        viewBox="0 0 26 26"
        fill="none"
      >
        <template v-if="rank === 1">
          <path
            d="M8.6125 11.5213C9.1797 10.9324 9.85993 10.4641 10.6124 10.1443C11.365 9.82463 12.1743 9.66011 12.9919 9.66063C14.5844 9.66063 16.1687 10.2781 17.3712 11.5213C19.7844 13.9994 19.7844 18.0294 17.3712 20.5075C16.8046 21.0971 16.1244 21.5658 15.3718 21.8856C14.6192 22.2054 13.8096 22.3695 12.9919 22.3681C11.3425 22.3681 9.7825 21.71 8.6125 20.5075C7.44273 19.3053 6.79204 17.6917 6.80062 16.0144C6.80062 14.3163 7.4425 12.7238 8.6125 11.5213ZM12.2492 12.2208C12.0393 12.2211 11.8376 12.3027 11.6865 12.4484C11.5353 12.5941 11.4464 12.7926 11.4385 13.0024C11.4305 13.2122 11.504 13.4169 11.6436 13.5737C11.7833 13.7305 11.9782 13.8271 12.1875 13.8434V18.6875C12.1875 18.903 12.2731 19.1097 12.4255 19.262C12.5778 19.4144 12.7845 19.5 13 19.5C13.2155 19.5 13.4221 19.4144 13.5745 19.262C13.7269 19.1097 13.8125 18.903 13.8125 18.6875V13.0333C13.8125 12.8178 13.7269 12.6112 13.5745 12.4588C13.4221 12.3064 13.2155 12.2208 13 12.2208H12.2492Z"
            fill="#fed330"
          />
          <path
            d="M11.0077 2.38063L12.9984 5.65256L14.989 2.38063C15.2652 1.90938 15.7609 1.625 16.3052 1.625H21.6596C22.8377 1.625 23.5771 2.8925 23.0084 3.91625C21.9384 5.81486 20.4247 7.42618 18.5965 8.6125C18.3049 8.83635 17.9808 9.01419 17.6353 9.13981C19.7592 10.6494 21.1494 13.1649 21.1494 16.0144C21.1494 20.6318 17.4972 24.375 12.9919 24.375C8.48656 24.375 4.83437 20.6318 4.83437 16.0144C4.83437 13.1625 6.22781 10.6438 8.35412 9.13575C8.01372 9.01127 7.69468 8.83475 7.40837 8.6125C5.58199 7.42404 4.0687 5.81321 2.99649 3.91625C2.41962 2.8925 3.15899 1.625 4.33712 1.625H9.69149C10.2359 1.625 10.7315 1.90938 11.0077 2.38063ZM12.7294 7.65781C14.0845 7.61169 15.4288 7.91233 16.6351 8.53125C17.1687 8.49029 17.6788 8.2943 18.1025 7.96737L18.1269 7.94787L18.1545 7.93162C19.8717 6.81694 21.2937 5.30323 22.2991 3.51975C22.3599 3.40854 22.3908 3.28343 22.3887 3.15668C22.3866 3.02994 22.3516 2.90591 22.2871 2.79677C22.2226 2.68763 22.1309 2.59713 22.0209 2.53415C21.9109 2.47116 21.7864 2.43786 21.6596 2.4375H16.3052C16.1804 2.43676 16.0576 2.46917 15.9494 2.5314C15.8412 2.59364 15.7515 2.68348 15.6894 2.79175L15.6861 2.79744L12.7294 7.65781ZM7.92999 21.2144C8.5862 21.8966 9.37364 22.4391 10.2449 22.8091C11.1162 23.1791 12.0534 23.3691 13 23.3675C14.9094 23.3675 16.7131 22.6038 18.0781 21.2144C20.8731 18.3463 20.8731 13.6825 18.0781 10.8144C17.4198 10.134 16.6313 9.59296 15.7596 9.22347C14.8879 8.85398 13.9508 8.66357 13.0041 8.66357C12.0573 8.66357 11.1202 8.85398 10.2485 9.22347C9.37683 9.59296 8.58835 10.134 7.92999 10.8144C6.57255 12.2039 5.81659 14.0719 5.82562 16.0144C5.82562 17.9806 6.57312 19.825 7.92999 21.2144Z"
            fill="#fed330"
          />
        </template>
        <template v-if="rank === 2">
          <path
            d="M8.6125 11.5213C9.1797 10.9324 9.85993 10.4641 10.6124 10.1443C11.365 9.82463 12.1743 9.66011 12.9919 9.66063C14.5844 9.66063 16.1687 10.2781 17.3712 11.5213C19.7844 13.9994 19.7844 18.0294 17.3712 20.5075C16.8046 21.0971 16.1244 21.5658 15.3718 21.8856C14.6192 22.2054 13.8096 22.3695 12.9919 22.3681C11.3425 22.3681 9.7825 21.71 8.6125 20.5075C7.44273 19.3053 6.79204 17.6917 6.80062 16.0144C6.80062 14.3163 7.4425 12.7238 8.6125 11.5213ZM11.2677 19.4594H14.4934C14.9484 19.4594 15.3059 19.0856 15.3059 18.6306C15.3059 18.1756 14.9402 17.81 14.4852 17.81H12.9902L14.5746 15.86C15.1109 15.2019 15.2246 14.3163 14.8752 13.5363C14.7102 13.1668 14.4459 12.8504 14.1116 12.6223C13.7773 12.3942 13.3863 12.2634 12.9821 12.2444H12.8765C12.0884 12.2444 11.349 12.6263 10.894 13.2681C10.634 13.6419 10.7152 14.1538 11.089 14.4138C11.4627 14.6738 11.9746 14.5844 12.2346 14.2188C12.389 14.0075 12.6246 13.8856 12.8846 13.8856H12.9252C13.2259 13.8938 13.3559 14.1131 13.3965 14.2106C13.429 14.2919 13.5265 14.5681 13.3152 14.8281L10.634 18.1188C10.4309 18.3625 10.3902 18.7038 10.5284 18.9881C10.5936 19.1292 10.698 19.2485 10.829 19.332C10.9601 19.4156 11.1123 19.4598 11.2677 19.4594Z"
            fill="#a5b1c2"
          />
          <path
            d="M12.9984 5.65256L11.0077 2.38063C10.7315 1.90938 10.2359 1.625 9.69149 1.625H4.33712C3.15899 1.625 2.41962 2.8925 2.99649 3.91625C4.0687 5.81321 5.58199 7.42404 7.40837 8.6125C7.69599 8.83594 8.01531 9.01144 8.35412 9.13575C6.22781 10.6438 4.83437 13.1625 4.83437 16.0144C4.83437 20.6318 8.48656 24.375 12.9919 24.375C17.4972 24.375 21.1494 20.6318 21.1494 16.0144C21.1494 13.1649 19.7592 10.6494 17.6361 9.13981C17.9812 9.01385 18.305 8.83603 18.5965 8.6125C20.4246 7.42625 21.9359 5.8175 23.0084 3.91625C23.5771 2.8925 22.8377 1.625 21.6596 1.625H16.3052C15.7609 1.625 15.2652 1.90938 14.989 2.38063L12.9984 5.65256ZM12.9919 7.65375C12.9041 7.65375 12.8164 7.65538 12.7294 7.65781L15.6869 2.79744L15.6902 2.79175C15.7522 2.6836 15.8419 2.59384 15.9499 2.53161C16.0579 2.46938 16.1806 2.43691 16.3052 2.4375H21.6596C21.7864 2.43786 21.9109 2.47116 22.0209 2.53415C22.1309 2.59713 22.2226 2.68763 22.2871 2.79677C22.3516 2.90591 22.3866 3.02994 22.3887 3.15668C22.3908 3.28343 22.3599 3.40854 22.2991 3.51975C21.2935 5.30333 19.8712 6.81704 18.1537 7.93162L18.1277 7.94787L18.1025 7.96737C17.6785 8.29445 17.1682 8.49044 16.6343 8.53125C15.5072 7.9532 14.2585 7.65237 12.9919 7.65375ZM7.92999 21.2144C6.57255 19.8249 5.81659 17.9569 5.82562 16.0144C5.82562 14.0481 6.57312 12.2038 7.92999 10.8144C8.58835 10.134 9.37683 9.59296 10.2485 9.22347C11.1202 8.85398 12.0573 8.66357 13.0041 8.66357C13.9508 8.66357 14.8879 8.85398 15.7596 9.22347C16.6313 9.59296 17.4198 10.134 18.0781 10.8144C20.8731 13.6825 20.8731 18.3463 18.0781 21.2144C16.7131 22.6038 14.9094 23.3675 13 23.3675C11.0825 23.3675 9.27874 22.6038 7.92999 21.2144Z"
            fill="#a5b1c2"
          />
        </template>
        <template v-if="rank === 3">
          <path
            d="M8.6125 11.5212C7.44273 12.7234 6.79204 14.337 6.80062 16.0144C6.80062 17.7125 7.4425 19.305 8.6125 20.5075C9.17967 21.0964 9.85989 21.5648 10.6124 21.8845C11.3649 22.2042 12.1743 22.3687 12.9919 22.3681C14.6494 22.3681 16.2012 21.71 17.3712 20.5075C19.7844 18.0294 19.7844 13.9994 17.3712 11.5212C16.8045 10.9317 16.1244 10.463 15.3718 10.1432C14.6191 9.82347 13.8096 9.65931 12.9919 9.66062C11.4075 9.66062 9.815 10.2781 8.6125 11.5212ZM14.9906 13.377L14.1391 14.8476C14.5264 15.0635 14.8492 15.3786 15.0743 15.7606C15.2993 16.1426 15.4185 16.5777 15.4196 17.0211C15.4192 17.6782 15.158 18.3084 14.6934 18.7732C14.2287 19.2379 13.5987 19.4994 12.9415 19.5C12.4313 19.4981 11.934 19.3393 11.5171 19.0451C11.1002 18.7509 10.784 18.3356 10.6112 17.8555C10.576 17.7579 10.5605 17.6543 10.5655 17.5507C10.5706 17.447 10.5962 17.3454 10.6407 17.2517C10.6853 17.158 10.748 17.0741 10.8253 17.0048C10.9025 16.9355 10.9927 16.8822 11.0906 16.848C11.2878 16.7789 11.5044 16.7904 11.6931 16.8802C11.8818 16.97 12.0274 17.1307 12.0981 17.3274C12.16 17.5015 12.2743 17.6523 12.4253 17.7588C12.5763 17.8653 12.7567 17.9224 12.9415 17.9221C13.4371 17.9221 13.8417 17.5167 13.8417 17.0211C13.8417 16.5254 13.4371 16.1208 12.9415 16.1208C12.731 16.1208 12.5291 16.0372 12.3802 15.8883C12.2313 15.7394 12.1477 15.5375 12.1477 15.327C12.1485 15.1494 12.2098 14.9775 12.3216 14.8395L12.9326 13.7743H11.7926C11.5866 13.7677 11.3912 13.6812 11.2478 13.5332C11.1044 13.3851 11.0242 13.187 11.0242 12.9809C11.0242 12.7748 11.1044 12.5767 11.2478 12.4286C11.3912 12.2806 11.5866 12.1941 11.7926 12.1875H14.3049C14.444 12.1872 14.5807 12.2237 14.7012 12.2933C14.8216 12.363 14.9215 12.4633 14.9906 12.584C15.0596 12.7048 15.0959 12.8414 15.0959 12.9805C15.0959 13.1196 15.0596 13.2562 14.9906 13.377Z"
            fill="#F29559"
          />
          <path
            d="M12.9984 5.65256L11.0077 2.38063C10.7315 1.90938 10.2359 1.625 9.69149 1.625H4.33712C3.15899 1.625 2.41962 2.8925 2.99649 3.91625C4.0687 5.81321 5.58199 7.42404 7.40837 8.6125C7.69599 8.83594 8.01531 9.01144 8.35412 9.13575C6.22781 10.6438 4.83437 13.1625 4.83437 16.0144C4.83437 20.6318 8.48656 24.375 12.9919 24.375C17.4972 24.375 21.1494 20.6318 21.1494 16.0144C21.1494 13.1649 19.7592 10.6494 17.6361 9.13981C17.9812 9.01385 18.305 8.83603 18.5965 8.6125C20.4246 7.42625 21.9359 5.8175 23.0084 3.91625C23.5771 2.8925 22.8377 1.625 21.6596 1.625H16.3052C15.7609 1.625 15.2652 1.90938 14.989 2.38063L12.9984 5.65256ZM12.9919 7.65375C12.9041 7.65375 12.8164 7.65538 12.7294 7.65781L15.6869 2.79744L15.6902 2.79175C15.7522 2.6836 15.8419 2.59384 15.9499 2.53161C16.0579 2.46938 16.1806 2.43691 16.3052 2.4375H21.6596C21.7864 2.43786 21.9109 2.47116 22.0209 2.53415C22.1309 2.59713 22.2226 2.68763 22.2871 2.79677C22.3516 2.90591 22.3866 3.02994 22.3887 3.15668C22.3908 3.28343 22.3599 3.40854 22.2991 3.51975C21.2935 5.30333 19.8712 6.81704 18.1537 7.93162L18.1277 7.94787L18.1025 7.96737C17.6785 8.29445 17.1682 8.49044 16.6343 8.53125C15.5072 7.9532 14.2585 7.65237 12.9919 7.65375ZM7.92999 21.2144C6.57255 19.8249 5.81659 17.9569 5.82562 16.0144C5.82562 14.0481 6.57312 12.2038 7.92999 10.8144C8.58835 10.134 9.37683 9.59296 10.2485 9.22347C11.1202 8.85398 12.0573 8.66357 13.0041 8.66357C13.9508 8.66357 14.8879 8.85398 15.7596 9.22347C16.6313 9.59296 17.4198 10.134 18.0781 10.8144C20.8731 13.6825 20.8731 18.3463 18.0781 21.2144C16.7131 22.6038 14.9094 23.3675 13 23.3675C11.0825 23.3675 9.27874 22.6038 7.92999 21.2144Z"
            fill="#F29559"
          />
        </template>
      </svg>
    </template>
    <template v-else>
      {{ rank }}
    </template>
  </div>
</template>

<script setup lang="ts">
defineProps({
  rank: {
    type: Number,
    default: -1,
  },
});
</script>
