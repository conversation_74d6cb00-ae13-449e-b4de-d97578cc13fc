import { courseTimer } from "~/composables/courses/courseTimer";
import { useGameMode } from "~/composables/main/game";
import { useInput } from "~/composables/main/question";
import { useSummary } from "~/composables/main/summary";
import { useAutoNextQuestion } from "~/composables/user/autoNext";
import { useKeyboardSound } from "~/composables/user/sound";
import { useSpaceSubmitAnswer } from "~/composables/user/submitKey";
import { useCourseStore } from "~/store/course";
import { useQuestionInput } from "./questionInputHelper";
import { useAnswerError } from "./useAnswerError";
import { usePlayTipSound, useTypingSound } from "./useTypingSound";

// 智能分割日语文本，基于罗马音的单词数量
function splitJapaneseByRomaji(japanese: string, romajiWords: string[]): string[] {
  // 1. 如果日语包含空格，按空格分割
  if (japanese.includes(" ")) {
    return japanese.split(" ");
  }

  // 2. 预定义的常见分割模式
  const knownSplits: Record<string, string[]> = {
    私は: ["私", "は"],
    私は小明です: ["私", "は", "小明", "です"],
    どういたしまして: ["どういたしまして"], // 作为整体
    こんにちは: ["こんにちは"], // 作为整体
    ありがとう: ["ありがとう"], // 作为整体
    すみません: ["すみません"], // 作为整体
    おはよう: ["おはよう"], // 作为整体
  };

  // 3. 检查是否有预定义的分割
  if (knownSplits[japanese]) {
    return knownSplits[japanese];
  }

  // 4. 基于罗马音单词数量的智能分割
  if (romajiWords.length === 2) {
    // 常见的助词模式
    const particlePattern = /^(.+)(は|が|を|に|で|と|から|まで|より)$/;
    const match = japanese.match(particlePattern);
    if (match) {
      return [match[1], match[2]];
    }

    // 默认：按字符数平均分割（作为最后手段）
    const midPoint = Math.ceil(japanese.length / 2);
    return [japanese.substring(0, midPoint), japanese.substring(midPoint)];
  }

  if (romajiWords.length === 3) {
    // 三个单词的常见模式：主语 + 助词 + 谓语/宾语
    if (japanese.length >= 3) {
      const third = Math.ceil(japanese.length / 3);
      return [
        japanese.substring(0, third),
        japanese.substring(third, third * 2),
        japanese.substring(third * 2),
      ];
    }
  }

  // 5. 其他情况：返回整个日语文本作为单个单词
  return [japanese];
}

export function useWrapperQuestionInput() {
  const courseStore = useCourseStore();
  const { showAnswer } = useGameMode();
  const { showSummary } = useSummary();
  const { setInputCursorPosition, getInputCursorPosition, blurInput, focusInput } =
    useQuestionInput();
  const { isKeyboardSoundEnabled } = useKeyboardSound();
  const { checkPlayTypingSound, playTypingSound } = useTypingSound();
  const { handleAnswerError } = useAnswerError();
  const { playRightSound } = usePlayTipSound();
  const { isAutoNextQuestion } = useAutoNextQuestion();
  const { isUseSpaceSubmitAnswer } = useSpaceSubmitAnswer();

  const {
    initialize: initializeQuestionInput,
    findWordById,
    inputValue,
    submitAnswer,
    setInputValue,
    handleKeyboardInput,
    isFixMode,
    isFixInputMode,
  } = useInput({
    source: () =>
      courseStore.currentStatement?.romaji || courseStore.currentStatement?.english || "",
    setInputCursorPosition,
    getInputCursorPosition,
    inputChangedCallback,
    getAlternativeAnswers: () => {
      // 提供日语作为替代答案
      const japanese = courseStore.currentStatement?.japanese || "";
      const romaji =
        courseStore.currentStatement?.romaji || courseStore.currentStatement?.english || "";

      // 处理空格：日语通常不包含空格，需要根据罗马音的空格位置来分割
      if (japanese && romaji) {
        const romajiWords = romaji.split(" ");
        if (romajiWords.length > 1) {
          // 如果罗马音有多个单词，尝试智能分割日语
          return splitJapaneseByRomaji(japanese, romajiWords);
        } else {
          // 单个单词，直接返回日语
          return [japanese];
        }
      }

      return [];
    },
  });

  function inputChangedCallback(e: KeyboardEvent) {
    if (isKeyboardSoundEnabled() && checkPlayTypingSound(e)) {
      playTypingSound();
    }
  }

  function handleAnswerRight() {
    courseTimer.timeEnd(String(courseStore.statementIndex)); // 停止当前题目的计时
    playRightSound();

    if (isAutoNextQuestion()) {
      // 自动下一题
      if (courseStore.isAllDone()) {
        blurInput(); // 失去输入焦点，防止结束时光标仍然在输入框，造成后续结算面板回车事件无法触发
        showSummary();
      }
      courseStore.toNextStatement();
    } else {
      showAnswer();
    }
  }

  return {
    initializeQuestionInput,
    isFixMode,
    isFixInputMode,
    findWordById,
    inputValue,
    setInputValue,
    submitAnswer() {
      submitAnswer(handleAnswerRight, handleAnswerError);
      focusInput();
    },
    handleKeyboardInput(e: KeyboardEvent) {
      handleKeyboardInput(e, {
        useSpaceSubmitAnswer: {
          enable: isUseSpaceSubmitAnswer(),
          rightCallback: handleAnswerRight,
          errorCallback: handleAnswerError,
        },
      });
    },
  };
}
