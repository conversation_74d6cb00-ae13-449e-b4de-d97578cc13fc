<template>
  <div class="text-center">
    <div class="ml-8 inline-flex flex-wrap items-center justify-center gap-1 text-5xl">
      <!-- 调试：直接显示日语文本 -->
      <span class="cursor-pointer p-1 hover:text-fuchsia-500">
        {{ courseStore.currentStatement?.japanese || "测试文本" }}
      </span>
      <UIcon
        name="i-ph-speaker-simple-high"
        class="ml-1 inline-block h-7 w-7 cursor-pointer text-gray-500 hover:text-fuchsia-500"
        @click="handlePlayJapaneseSound"
      ></UIcon>
    </div>
    <div class="my-6 text-xl text-gray-500">
      {{ courseStore.currentStatement?.hiragana || "" }}
    </div>
    <div class="my-6 text-xl text-gray-500">
      {{ courseStore.currentStatement?.romaji }}
    </div>
    <div class="my-6 text-xl text-gray-500">
      {{ courseStore.currentStatement?.chinese }}
    </div>
    <div class="space-y-3">
      <div>
        <button
          class="btn btn-outline btn-sm"
          @click="showQuestion"
        >
          再来一次
        </button>
        <button
          class="btn btn-outline btn-sm ml-6"
          @click="goToNextQuestion"
        >
          下一题
        </button>
      </div>
      <div class="md:hidden">
        <MainMasteredBtn></MainMasteredBtn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from "vue";

import { useCurrentStatementJapaneseSound } from "~/composables/main/englishSound";
import { usePlayWordSound } from "~/composables/main/englishSound/audio";
import { useGameMode } from "~/composables/main/game";
import { useAutoPronunciation } from "~/composables/user/sound";
import { useCourseStore } from "~/store/course";
import { cancelShortcut, registerShortcut } from "~/utils/keyboardShortcuts";
import { useAnswer } from "./QuestionInput/useAnswer";

const courseStore = useCourseStore();
const { handlePlayWordSound } = usePlayWordSound();
const { handlePlayJapaneseSound } = usePlayJapaneseSound();
const { showQuestion } = useGameMode();
const { isAutoPlaySound } = useAutoPronunciation();
const { goToNextQuestion } = useAnswer();

const words = computed(() => {
  // 直接显示日语文本，如果没有则显示英语
  const text =
    courseStore.currentStatement?.japanese || courseStore.currentStatement?.english || "";

  console.log("Answer.vue - currentStatement:", courseStore.currentStatement);
  console.log("Answer.vue - text:", text);

  // 如果文本为空，返回空数组
  if (!text) {
    console.log("Answer.vue - 文本为空，返回空数组");
    return [];
  }

  // 日语文本直接作为整体显示，不分割
  const result = [text];
  console.log("Answer.vue - words result:", result);
  return result;
});

function usePlayJapaneseSound() {
  const { playSound } = useCurrentStatementJapaneseSound();

  onMounted(() => {
    if (isAutoPlaySound()) {
      playSound();
    }
  });

  function handlePlayJapaneseSound() {
    playSound();
  }

  return {
    handlePlayJapaneseSound,
  };
}
</script>
