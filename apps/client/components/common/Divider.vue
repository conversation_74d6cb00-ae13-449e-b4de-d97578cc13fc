<template>
  <div class="w-full pt-24">
    <span class="relative flex justify-center">
      <div
        class="absolute inset-x-0 top-1/2 h-px -translate-y-1/2 bg-transparent bg-gradient-to-r from-transparent via-gray-500 to-transparent opacity-75"
      ></div>
      <div
        class="absolute -top-1 flex h-2 w-5 items-center justify-center rounded-lg bg-gray-600 dark:bg-gray-400 lg:-top-2 lg:h-3.5 lg:w-8"
      >
        <div class="h-1 w-3.5 rounded-lg bg-white dark:bg-theme-dark lg:h-2 lg:w-6"></div>
      </div>
    </span>
  </div>
</template>

<script setup lang="ts"></script>

<style scoped></style>
