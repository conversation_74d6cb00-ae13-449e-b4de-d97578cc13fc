{"name": "client", "version": "1.4.21", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate --dotenv .env.prod", "preview": "nuxt preview", "postinstall": "nuxt prepare", "test": "pnpm run test:unit:run", "test:unit:watch": "vitest", "test:unit:run": "vitest run", "test:e2e:run": "cypress run", "test:ci": "start-server-and-test dev http://localhost:3000 test", "cypress:open": "cypress open", "type-check": "nuxi typecheck"}, "devDependencies": {"@hypernym/nuxt-anime": "^2.1.1", "@iconify-json/ph": "^1.1.12", "@iconify-json/simple-icons": "^1.1.99", "@nuxt/image": "^1.7.0", "@nuxt/test-utils": "^3.10.0", "@nuxt/ui": "^2.18.4", "@pinia/testing": "^0.1.3", "@types/google-libphonenumber": "^7.4.30", "@types/lodash-es": "^4.17.12", "@vue/test-utils": "^2.4.3", "@vueuse/core": "^10.7.2", "@vueuse/nuxt": "^10.7.2", "cypress": "^13.6.4", "daisyui": "^4.6.0", "happy-dom": "^13.3.1", "nuxt": "^3.9.0", "start-server-and-test": "^2.0.3", "tailwindcss": "^3.4.1", "vfonts": "^0.0.3", "vite": "^5.1.0", "vitest": "^1.2.2", "vue": "^3.4.6", "vue-router": "^4.2.5"}, "dependencies": {"@logto/vue": "^2.2.5", "@types/canvas-confetti": "^1.6.4", "@types/country-telephone-data": "^0.6.3", "canvas-confetti": "^1.9.2", "country-telephone-data": "^0.6.3", "dayjs": "^1.11.10", "fuse.js": "^7.0.0", "google-libphonenumber": "^3.2.34", "lodash-es": "^4.17.21", "ofetch": "^1.3.4", "pinia": "^2.1.7", "satori": "^0.10.13", "vee-validate": "^4.12.5", "vue-sonner": "^1.1.4", "yup": "^1.3.3"}}