{"name": "earthworm", "version": "1.0.0", "description": "", "packageManager": "pnpm@9.3.0+sha256.e1f9e8d1a16607a46dd3c158b5f7a7dc7945501d1c6222d454d63d033d1d918f", "main": "index.js", "scripts": {"prepare": "simple-git-hooks", "schema:build": "pnpm -F @earthworm/schema build", "db:init": "pnpm schema:build && pnpm -F @earthworm/db run init", "db:init:test": "pnpm schema:build && pnpm -F @earthworm/db run init:test", "db:init:test:ci": "pnpm schema:build && pnpm -F @earthworm/db run init:ci", "db:studio": "pnpm -F @earthworm/db db:studio", "db:upload": "pnpm -F @earthworm/xingrong-courses upload", "dev:serve": "pnpm schema:build && pnpm -F api start:dev", "dev:client": "pnpm -F client dev", "docker:start": "docker-compose up -d", "docker:stop": "docker-compose stop", "docker:delete": "docker-compose down", "docker:down": "docker-compose down --volumes", "build:server": "pnpm schema:build && pnpm -F api build", "build:client": "pnpm -F client generate", "prod:serve": "pnpm -F api start:prod:pm", "format": "prettier --write --cache .", "format-check": "prettier --check --cache .", "test": "pnpm -F api test && pnpm -F client test", "test:ci": "pnpm -F api test && pnpm -F client test:ci", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "docs:dev": "pnpm -F docs docs:dev", "build:docs": "pnpm -F docs docs:build", "release": "tsx --env-file=.env scripts/release.ts"}, "keywords": [], "author": "", "license": "ISC", "engines": {"node": ">=20.12.2"}, "devDependencies": {"@ianvs/prettier-plugin-sort-imports": "^4.2.1", "@types/archiver": "^6.0.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/node": "^20.12.7", "@types/semver": "^7.5.8", "archiver": "^7.0.1", "conventional-changelog-cli": "^4.1.0", "dotenv": "^16.3.1", "fs-extra": "^11.2.0", "inquirer": "^9.2.13", "lint-staged": "^15.2.2", "picocolors": "^1.0.1", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "0.5.13", "semver": "^7.6.3", "simple-git-hooks": "2.11.1", "tsup": "^8.0.1", "tsx": "^4.7.0", "typescript": "^5.3.3"}, "dependencies": {"@t3-oss/env-core": "^0.7.1", "zod": "^3.22.4"}}