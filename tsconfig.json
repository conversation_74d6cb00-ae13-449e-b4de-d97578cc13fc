{"compilerOptions": {"baseUrl": ".", "forceConsistentCasingInFileNames": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "target": "ESNext", "module": "ESNext", "moduleResolution": "Node", "strict": true, "noImplicitAny": true, "allowJs": true, "noEmit": true, "noUnusedLocals": true, "resolveJsonModule": true, "types": ["node"], "paths": {"@shared/*": ["libs/shared/src/*"]}}, "exclude": ["**/dist/**", "**/.nuxt/**", "**/nuxt.d.ts", "**/examples/**", "**/docs/**", "**/playground/**", "**/test/**"]}