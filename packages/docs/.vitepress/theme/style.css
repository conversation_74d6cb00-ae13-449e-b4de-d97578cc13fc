/**
 * Colors
 * -------------------------------------------------------------------------- */

:root {
  --c-yellow-1: #a963ea;
  --c-yellow-2: #a76fdc;
  --c-yellow-3: #b280e1;
  --c-yellow-soft-1: #a963ea;
  --c-yellow-soft-2: #7c3abb;

  --c-teal: #086367;
  --c-teal-light: #33898d;

  --c-white-dark: #f8f8f8;
  --c-black-darker: #0d121b;
  --c-black: #111827;
  --c-black-light: #161f32;
  --c-black-lighter: #262a44;

  --c-green-1: #52ce63;
  --c-green-2: #8ae99c;
  --c-green-3: #51a256;
  --c-green-soft: #316334;

  --c-text-dark-1: #d9e6eb;
  --c-text-dark-2: #c4dde6;
  --c-text-dark-3: #abc4cc;
  --c-text-light-1: #2c3e50;
  --c-text-light-2: #476582;
  --c-text-light-3: #90a4b7;

  --code-font-family: "dm", source-code-pro, Menlo, Monaco, Consolas, "Courier New", monospace;
  --code-font-size: 16px;

  /* 链接和高亮语法的颜色 */
  --highlight-color: #3451b2;
}

html.dark:root {
  --vp-c-brand-1: var(--c-yellow-1);
  --vp-c-brand-2: var(--c-yellow-2);
  --vp-c-brand-3: var(--c-yellow-3);

  --vp-c-bg: var(--c-black);
  --vp-c-bg-soft: var(--c-black-light);
  --vp-c-bg-alt: #0d121b;

  --vp-code-line-highlight-color: rgba(0, 0, 0, 0.5);

  --vp-c-text-1: var(--c-text-dark-1);
  --vp-c-brand-text: var(--c-text-light-1);

  --highlight-color: #a76fdc;
}

/**
 * Component: Button
 * -------------------------------------------------------------------------- */

:root {
  --vp-button-brand-border: var(--c-yellow-soft-1);
  --vp-button-brand-text: var(--c-black);
  --vp-button-brand-bg: var(--c-yellow-1);
  --vp-button-brand-hover-border: var(--c-yellow-2);
  --vp-button-brand-hover-text: var(--c-black-darker);
  --vp-button-brand-hover-bg: var(--c-yellow-2);
  --vp-button-brand-active-border: var(--c-yellow-soft-1);
  --vp-button-brand-active-text: var(--c-black-darker);
  --vp-button-brand-active-bg: var(--vp-button-brand-bg);
}

/**
 * Component: Home
 * -------------------------------------------------------------------------- */

:root {
  --vp-home-hero-name-color: transparent;
  --vp-home-hero-name-background: linear-gradient(292deg, var(--c-green-1) 50%, var(--c-green-2));
  --vp-home-hero-image-background-image: linear-gradient(
    15deg,
    var(--c-yellow-2) 65%,
    var(--c-green-1) 30%
  );
  --vp-home-hero-image-filter: blur(40px);
}

.VPHero .VPImage.image-src {
  max-height: 192px;
}

@media (min-width: 640px) {
  :root {
    --vp-home-hero-image-filter: blur(56px);
  }

  .VPHero .VPImage.image-src {
    max-height: 256px;
  }
}

@media (min-width: 960px) {
  :root {
    --vp-home-hero-image-filter: blur(72px);
  }

  .VPHero .VPImage.image-src {
    max-height: 320px;
  }
}

.vp-doc a {
  text-decoration: none;
  color: var(--highlight-color);
  /* 应用链接颜色 */
}

.vp-doc a:hover {
  text-decoration: underline;
  color: var(--highlight-color);
  /* 应用链接颜色 */
}

.vp-doc code {
  color: var(--highlight-color);
  /* 应用高亮语法的颜色 */
}

.sponsors-top .become-sponsor {
  font-size: 0.75em;
  padding: 0.2em;
  width: auto;
  max-width: 150px;
}

#local-search {
  width: 400px;
}
