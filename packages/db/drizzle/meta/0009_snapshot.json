{"id": "84dd4daf-5de7-4934-b7df-5a3d324a3f46", "prevId": "dbe0f280-e91d-405d-b81a-bc8148305d69", "version": "7", "dialect": "postgresql", "tables": {"public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "video": {"name": "video", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"courses_course_pack_id_course_packs_id_fk": {"name": "courses_course_pack_id_course_packs_id_fk", "tableFrom": "courses", "tableTo": "course_packs", "columnsFrom": ["course_pack_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.course_history": {"name": "course_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "completion_count": {"name": "completion_count", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"course_history_user_id_course_id_course_pack_id_unique": {"name": "course_history_user_id_course_id_course_pack_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "course_id", "course_pack_id"]}}}, "public.course_packs": {"name": "course_packs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "is_free": {"name": "is_free", "type": "boolean", "primaryKey": false, "notNull": false}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": false}, "creator_id": {"name": "creator_id", "type": "text", "primaryKey": false, "notNull": true}, "share_level": {"name": "share_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'private'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.statements": {"name": "statements", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": true}, "english": {"name": "english", "type": "text", "primaryKey": false, "notNull": true}, "soundmark": {"name": "soundmark", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"statements_course_id_courses_id_fk": {"name": "statements_course_id_courses_id_fk", "tableFrom": "statements", "tableTo": "courses", "columnsFrom": ["course_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_learn_record": {"name": "user_learn_record", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "day": {"name": "day", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_learn_record_user_id_day_unique": {"name": "user_learn_record_user_id_day_unique", "nullsNotDistinct": false, "columns": ["user_id", "day"]}}}, "public.user_course_progress": {"name": "user_course_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "statement_index": {"name": "statement_index", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_course_progress_user_id_course_pack_id_unique": {"name": "user_course_progress_user_id_course_pack_id_unique", "nullsNotDistinct": false, "columns": ["user_id", "course_pack_id"]}}}, "public.memberships": {"name": "memberships", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true, "default": "'regular'"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_learning_activities": {"name": "user_learning_activities", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "activity_type": {"name": "activity_type", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_learning_activities_user_id_date_activity_type_unique": {"name": "user_learning_activities_user_id_date_activity_type_unique", "nullsNotDistinct": false, "columns": ["user_id", "date", "activity_type"]}}}, "public.mastered_elements": {"name": "mastered_elements", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "jsonb", "primaryKey": false, "notNull": true}, "mastered_at": {"name": "mastered_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}