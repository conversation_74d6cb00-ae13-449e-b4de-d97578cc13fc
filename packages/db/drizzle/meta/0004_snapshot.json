{"version": "7", "dialect": "postgresql", "tables": {"public.courses": {"name": "courses", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "video": {"name": "video", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"courses_course_pack_id_course_packs_id_fk": {"name": "courses_course_pack_id_course_packs_id_fk", "tableFrom": "courses", "columnsFrom": ["course_pack_id"], "tableTo": "course_packs", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.course_history": {"name": "course_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "completion_count": {"name": "completion_count", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"course_history_user_id_course_id_course_pack_id_unique": {"name": "course_history_user_id_course_id_course_pack_id_unique", "columns": ["user_id", "course_id", "course_pack_id"], "nullsNotDistinct": false}}}, "public.course_packs": {"name": "course_packs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "default": "''"}, "is_free": {"name": "is_free", "type": "boolean", "primaryKey": false, "notNull": false}, "cover": {"name": "cover", "type": "text", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "share_level": {"name": "share_level", "type": "text", "primaryKey": false, "notNull": false, "default": "'private'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.memberships": {"name": "memberships", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.statements": {"name": "statements", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "chinese": {"name": "chinese", "type": "text", "primaryKey": false, "notNull": true}, "english": {"name": "english", "type": "text", "primaryKey": false, "notNull": true}, "soundmark": {"name": "soundmark", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"statements_course_id_courses_id_fk": {"name": "statements_course_id_courses_id_fk", "tableFrom": "statements", "columnsFrom": ["course_id"], "tableTo": "courses", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.user_course_progress": {"name": "user_course_progress", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "course_pack_id": {"name": "course_pack_id", "type": "text", "primaryKey": false, "notNull": true}, "course_id": {"name": "course_id", "type": "text", "primaryKey": false, "notNull": true}, "statement_index": {"name": "statement_index", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_course_progress_user_id_course_pack_id_unique": {"name": "user_course_progress_user_id_course_pack_id_unique", "columns": ["user_id", "course_pack_id"], "nullsNotDistinct": false}}}, "public.user_learn_record": {"name": "user_learn_record", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "day": {"name": "day", "type": "date", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_learn_record_user_id_day_unique": {"name": "user_learn_record_user_id_day_unique", "columns": ["user_id", "day"], "nullsNotDistinct": false}}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "38496a18-78fe-4836-99b0-8ca3838e3230", "prevId": "33840690-e39a-4529-9ebf-e28edf1f9798"}