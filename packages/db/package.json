{"name": "@earthworm/db", "version": "1.0.0", "description": "", "main": "src/index.ts", "scripts": {"init:ci": "tsx src/migrate.ts", "init:test": "drizzle-kit push --config=drizzle.config.test.ts", "init": "drizzle-kit push", "db:studio": "drizzle-kit studio", "generate": "drizzle-kit generate", "migrate": "drizzle-kit migrate"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"drizzle-kit": "^0.23.0", "pg": "^8.11.5"}, "dependencies": {"drizzle-orm": "^0.32.0", "postgres": "^3.4.4", "@earthworm/schema": "workspace:^"}}